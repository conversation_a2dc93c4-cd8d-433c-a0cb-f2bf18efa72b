import React, { useState, useRef } from 'react';
import { audioConverter } from '../utils/audioConverter';
import { replicateClient } from '../utils/replicateClient';
import { downloadAllFormats, downloadFile, generateTXT, generateSRT, generateVTT } from '../utils/subtitleGenerator';
import { validateFileForReplicate, estimateProcessingTime, formatFileSize, splitFileIntoChunks, combineChunkResults } from '../utils/fileChunker';
import { REPLICATE_API_TOKEN } from '../config/apiConfig';

const TranscriptionApp = () => {
  // API token from config file
  const apiToken = REPLICATE_API_TOKEN;

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const [transcriptionResult, setTranscriptionResult] = useState(null);
  const [error, setError] = useState('');
  const [options, setOptions] = useState({
    language: 'en',
    diarization: false,
    huggingfaceToken: '',
    temperature: 0.0
  });

  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      setError('');
      setTranscriptionResult(null);

      // Validate file
      const validation = validateFileForReplicate(file);
      setFileValidation(validation);

      console.log(`📁 File selected: ${file.name} (${formatFileSize(file.size)})`);
      console.log(`⏱️ Estimated processing time: ${estimateProcessingTime(file.size)}`);

      if (validation.warnings.length > 0) {
        console.warn('⚠️ File validation warnings:', validation.warnings);
      }
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      setSelectedFile(file);
      setError('');
      setTranscriptionResult(null);

      // Validate file
      const validation = validateFileForReplicate(file);
      setFileValidation(validation);

      console.log(`📁 File dropped: ${file.name} (${formatFileSize(file.size)})`);
      console.log(`⏱️ Estimated processing time: ${estimateProcessingTime(file.size)}`);

      if (validation.warnings.length > 0) {
        console.warn('⚠️ File validation warnings:', validation.warnings);
      }
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const validateInputs = () => {
    if (apiToken === 'YOUR_REPLICATE_API_TOKEN_HERE') {
      throw new Error('Please set your Replicate API token in frontend/src/config/apiConfig.js');
    }
    if (!apiToken || apiToken.length < 10) {
      throw new Error('Invalid Replicate API token. Please check your token in the config file.');
    }
    if (!selectedFile) {
      throw new Error('Please select a media file');
    }
    if (options.diarization && !options.huggingfaceToken.trim()) {
      throw new Error('Hugging Face token is required when diarization is enabled');
    }
  };

  const startTranscription = async () => {
    try {
      validateInputs();

      setIsProcessing(true);
      setError('');
      setProgress(0);
      setTranscriptionResult(null);

      console.log('🚀 Starting transcription process...');

      // Set API token
      replicateClient.setApiToken(apiToken);

      // Check if file needs chunking
      const needsChunking = selectedFile.size > 100 * 1024 * 1024; // 100MB

      if (needsChunking) {
        console.log(`📦 Large file detected (${formatFileSize(selectedFile.size)}), will process in chunks`);
        await processLargeFile();
      } else {
        console.log(`📄 Regular file size (${formatFileSize(selectedFile.size)}), processing normally`);
        await processRegularFile();
      }

    } catch (err) {
      console.error('❌ Transcription error:', err);
      setError(err.message);

      // Log additional details for debugging
      if (err.response) {
        console.error('API Response:', err.response.status, err.response.data);
      }
    } finally {
      setIsProcessing(false);
      if (!transcriptionResult) {
        setCurrentStep('');
        setProgress(0);
      }
    }
  };

  const processRegularFile = async () => {
    // Step 1: Check FFmpeg availability and prepare audio
    setCurrentStep('Preparing audio for transcription...');
    setProgress(5);
    console.log('📦 Checking audio converter availability...');

    const canUseFFmpeg = await audioConverter.canUseFFmpeg();

    if (canUseFFmpeg) {
      console.log('✅ FFmpeg.wasm available - will convert to optimal format');
      setCurrentStep('Converting audio to WAV format...');
    } else {
      console.log('⚠️ FFmpeg.wasm not available - using original file format');
      setCurrentStep('Using original file format (WhisperX will handle conversion)...');
    }

    setProgress(10);
    console.log(`🎵 Processing ${selectedFile.name}...`);

    const audioBlob = await audioConverter.convertToWav(selectedFile, (conversionProgress) => {
      const totalProgress = 10 + Math.round(conversionProgress * 0.2); // 10-30% of total progress
      setProgress(totalProgress);
      console.log(`🔄 Audio processing: ${conversionProgress}% (Total: ${totalProgress}%)`);
    });

    console.log(`✅ Audio prepared successfully: ${(audioBlob.size / 1024 / 1024).toFixed(2)} MB`);

    // Step 2: Start transcription
    setCurrentStep('Uploading to Replicate API...');
    setProgress(35);
    console.log('📤 Starting Replicate transcription...');

    const prediction = await replicateClient.startTranscription(audioBlob, options);
    console.log(`✅ Transcription job started: ${prediction.id}`);

    // Step 3: Poll for completion
    setCurrentStep('Transcribing audio (this may take several minutes)...');
    setProgress(40);

    let pollCount = 0;
    const result = await replicateClient.pollTranscription(
      prediction.id,
      (predictionUpdate) => {
        pollCount++;
        console.log(`🔄 Poll ${pollCount}: Status = ${predictionUpdate.status}`);

        if (predictionUpdate.status === 'starting') {
          setCurrentStep('Transcription job starting...');
          setProgress(45);
        } else if (predictionUpdate.status === 'processing') {
          setCurrentStep('Processing audio with WhisperX...');
          // Gradually increase progress from 50% to 90%
          const processingProgress = 50 + Math.min(40, pollCount * 2);
          setProgress(processingProgress);
        }
      }
    );

    setProgress(95);
    setCurrentStep('Processing results...');
    console.log('✅ Transcription completed successfully');

    setProgress(100);
    setCurrentStep('Transcription completed!');
    setTranscriptionResult(result.output);

    console.log('🎉 All done! Results ready for download.');
  };

  const processLargeFile = async () => {
    // Step 1: Split file into chunks
    setCurrentStep('Splitting large file into chunks...');
    setProgress(5);

    const chunks = await splitFileIntoChunks(selectedFile, 25 * 1024 * 1024); // 25MB chunks
    console.log(`📦 File split into ${chunks.length} chunks`);

    // Step 2: Process each chunk
    const chunkResults = [];
    const totalChunks = chunks.length;

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      setCurrentStep(`Processing chunk ${i + 1} of ${totalChunks}...`);
      const baseProgress = 10 + (i / totalChunks) * 80; // 10-90% for all chunks
      setProgress(Math.round(baseProgress));

      console.log(`🔄 Processing chunk ${i + 1}/${totalChunks}: ${chunk.file.name}`);

      // Retry logic for each chunk
      let chunkSuccess = false;
      let retryCount = 0;
      const maxRetries = 3;

      while (!chunkSuccess && retryCount < maxRetries) {
        try {
          if (retryCount > 0) {
            console.log(`🔄 Retrying chunk ${i + 1}, attempt ${retryCount + 1}/${maxRetries}`);
            setCurrentStep(`Retrying chunk ${i + 1} (attempt ${retryCount + 1})...`);
            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
          }

          // Convert chunk to audio if needed
          const audioBlob = await audioConverter.convertToWav(chunk.file, (conversionProgress) => {
            const chunkProgress = baseProgress + (conversionProgress * 0.1); // Small progress increment per chunk
            setProgress(Math.round(chunkProgress));
          });

          // Start transcription for this chunk
          const prediction = await replicateClient.startTranscription(audioBlob, options);
          console.log(`✅ Chunk ${i + 1} transcription started: ${prediction.id}`);

          // Poll for completion
          const result = await replicateClient.pollTranscription(prediction.id, (update) => {
            console.log(`🔄 Chunk ${i + 1} status: ${update.status}`);
          });

          chunkResults.push(result.output);
          console.log(`✅ Chunk ${i + 1} completed successfully`);
          chunkSuccess = true;

        } catch (error) {
          retryCount++;
          console.error(`❌ Error processing chunk ${i + 1} (attempt ${retryCount}):`, error);

          // Check if this is a retryable error
          const isRetryable = error.message.includes('interrupted') ||
                             error.message.includes('PA') ||
                             error.message.includes('timeout') ||
                             error.message.includes('502') ||
                             error.message.includes('503') ||
                             error.message.includes('504');

          if (!isRetryable || retryCount >= maxRetries) {
            console.error(`❌ Chunk ${i + 1} failed permanently after ${retryCount} attempts`);
            setError(`Chunk ${i + 1} failed: ${error.message} (after ${retryCount} attempts)`);
            chunkResults.push(null);
            break;
          } else {
            console.log(`⏳ Will retry chunk ${i + 1} in ${2 * retryCount} seconds...`);
          }
        }
      }
    }

    // Step 3: Combine results
    setCurrentStep('Combining results from all chunks...');
    setProgress(95);

    const validResults = chunkResults.filter(result => result !== null);
    if (validResults.length === 0) {
      throw new Error('All chunks failed to process');
    }

    const combinedResult = combineChunkResults(validResults);

    setProgress(100);
    setCurrentStep('Transcription completed!');
    setTranscriptionResult(combinedResult);

    console.log(`🎉 Large file processing complete! Combined ${validResults.length}/${totalChunks} successful chunks.`);
  };

  const testApiConnection = async () => {
    try {
      setIsProcessing(true);
      setError('');
      setCurrentStep('Testing API connection...');
      setProgress(10);

      // Create a proper WAV file with minimal audio data
      const createTestWav = () => {
        const sampleRate = 16000;
        const duration = 1; // 1 second
        const numSamples = sampleRate * duration;
        const numChannels = 1;
        const bytesPerSample = 2;
        const blockAlign = numChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = numSamples * blockAlign;
        const fileSize = 44 + dataSize;

        const buffer = new ArrayBuffer(fileSize);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
          for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
          }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, fileSize - 8, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // fmt chunk size
        view.setUint16(20, 1, true); // PCM format
        view.setUint16(22, numChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bytesPerSample * 8, true); // bits per sample
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);

        // Audio data (silence)
        for (let i = 44; i < fileSize; i += 2) {
          view.setInt16(i, 0, true); // silence
        }

        return new Blob([buffer], { type: 'audio/wav' });
      };

      const testBlob = createTestWav();
      console.log(`📄 Created test WAV file: ${(testBlob.size / 1024).toFixed(2)} KB`);

      setCurrentStep('Sending test request to Replicate...');
      setProgress(50);

      replicateClient.setApiToken(apiToken);
      const prediction = await replicateClient.startTranscription(testBlob, { language: 'en' });

      setCurrentStep('Waiting for test response...');
      setProgress(75);

      const result = await replicateClient.pollTranscription(prediction.id);

      setProgress(100);
      setCurrentStep('API connection test successful!');

      console.log('✅ API test successful:', result);
      setTimeout(() => {
        setCurrentStep('');
        setProgress(0);
      }, 3000);

    } catch (error) {
      console.error('❌ API test failed:', error);
      setError(`API test failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownloadAll = () => {
    if (transcriptionResult) {
      const baseFilename = selectedFile ? selectedFile.name.replace(/\.[^/.]+$/, '') : 'transcription';
      downloadAllFormats(transcriptionResult, baseFilename);
    }
  };

  const handleDownloadFormat = (format) => {
    if (!transcriptionResult) return;

    const baseFilename = selectedFile ? selectedFile.name.replace(/\.[^/.]+$/, '') : 'transcription';
    let content, filename, mimeType;

    switch (format) {
      case 'txt':
        content = generateTXT(transcriptionResult);
        filename = `${baseFilename}.txt`;
        mimeType = 'text/plain';
        break;
      case 'srt':
        content = generateSRT(transcriptionResult);
        filename = `${baseFilename}.srt`;
        mimeType = 'text/plain';
        break;
      case 'vtt':
        content = generateVTT(transcriptionResult);
        filename = `${baseFilename}.vtt`;
        mimeType = 'text/vtt';
        break;
      default:
        return;
    }

    downloadFile(content, filename, mimeType);
  };

  return (
    <div className="transcription-app">
      <div className="container">
        <h1>WhisperX Transcription Tool</h1>
        <p className="subtitle">Convert media files to text with precise timestamps</p>



        {/* File Upload */}
        <div className="section">
          <label>Media File:</label>
          <div
            className={`file-drop-zone ${selectedFile ? 'has-file' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*,audio/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              disabled={isProcessing}
            />
            {selectedFile ? (
              <div className="file-info">
                <span className="file-name">{selectedFile.name}</span>
                <span className="file-size">({formatFileSize(selectedFile.size)})</span>
                <div className="file-details">
                  <small>⏱️ Estimated time: {estimateProcessingTime(selectedFile.size)}</small>
                </div>

                {fileValidation && (
                  <div className="file-validation">
                    {fileValidation.needsChunking && (
                      <div className="file-warning">
                        📦 Large file will be processed via proxy server
                      </div>
                    )}

                    {fileValidation.warnings.map((warning, index) => (
                      <div key={index} className="file-warning">
                        ⚠️ {warning}
                      </div>
                    ))}

                    {fileValidation.errors.map((error, index) => (
                      <div key={index} className="file-error">
                        ❌ {error}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="file-placeholder">
                <span>Click to select or drag & drop a media file</span>
                <small>Supports video and audio formats</small>
              </div>
            )}
          </div>
        </div>

        {/* Options */}
        <div className="section">
          <h3>Transcription Options</h3>
          <div className="options-grid">
            <div className="option">
              <label htmlFor="language">Language:</label>
              <select
                id="language"
                value={options.language}
                onChange={(e) => setOptions({...options, language: e.target.value})}
                disabled={isProcessing}
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
                <option value="pt">Portuguese</option>
                <option value="ru">Russian</option>
                <option value="ja">Japanese</option>
                <option value="ko">Korean</option>
                <option value="zh">Chinese</option>
              </select>
            </div>

            <div className="option">
              <label>
                <input
                  type="checkbox"
                  checked={options.diarization}
                  onChange={(e) => setOptions({...options, diarization: e.target.checked})}
                  disabled={isProcessing}
                />
                Speaker Diarization
              </label>
            </div>
          </div>

          {options.diarization && (
            <div className="option">
              <label htmlFor="hfToken">Hugging Face Token:</label>
              <input
                id="hfToken"
                type="password"
                value={options.huggingfaceToken}
                onChange={(e) => setOptions({...options, huggingfaceToken: e.target.value})}
                placeholder="Required for speaker diarization"
                disabled={isProcessing}
              />
              <small>Get your token from <a href="https://huggingface.co/settings/tokens" target="_blank" rel="noopener noreferrer">Hugging Face</a></small>
            </div>
          )}
        </div>

        {/* Process Buttons */}
        <div className="section">
          <div className="button-group">
            <button
              className="test-btn"
              onClick={testApiConnection}
              disabled={isProcessing}
            >
              {isProcessing ? 'Testing...' : 'Test API Connection'}
            </button>
            <button
              className="process-btn"
              onClick={startTranscription}
              disabled={isProcessing || !selectedFile}
            >
              {isProcessing ? 'Processing...' : 'Start Transcription'}
            </button>
          </div>
        </div>

        {/* Progress */}
        {isProcessing && (
          <div className="section">
            <div className="progress-container">
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <div className="progress-text">{currentStep} ({progress}%)</div>
              <div className="progress-details">
                <small>💡 Check browser console (F12) for detailed logs</small>
              </div>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="section">
            <div className="error-message">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {/* Results */}
        {transcriptionResult && (
          <div className="section">
            <h3>Transcription Complete!</h3>
            <div className="results-container">
              <div className="download-buttons">
                <button onClick={handleDownloadAll} className="download-btn primary">
                  Download All Formats
                </button>
                <button onClick={() => handleDownloadFormat('txt')} className="download-btn">
                  Download TXT
                </button>
                <button onClick={() => handleDownloadFormat('srt')} className="download-btn">
                  Download SRT
                </button>
                <button onClick={() => handleDownloadFormat('vtt')} className="download-btn">
                  Download VTT
                </button>
              </div>

              <div className="transcription-preview">
                <h4>Preview:</h4>
                <div className="preview-content">
                  {transcriptionResult.segments?.slice(0, 3).map((segment, index) => (
                    <div key={index} className="segment-preview">
                      <span className="timestamp">[{segment.start?.toFixed(2)}s - {segment.end?.toFixed(2)}s]</span>
                      <span className="text">{segment.text}</span>
                    </div>
                  ))}
                  {transcriptionResult.segments?.length > 3 && (
                    <div className="more-segments">
                      ... and {transcriptionResult.segments.length - 3} more segments
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptionApp;
