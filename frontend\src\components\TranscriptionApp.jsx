import React, { useState, useRef } from 'react';
import { audioConverter } from '../utils/audioConverter';
import { replicateClient } from '../utils/replicateClient';
import { downloadAllFormats, downloadFile, generateTXT, generateSRT, generateVTT } from '../utils/subtitleGenerator';

const TranscriptionApp = () => {
  const [apiToken, setApiToken] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const [transcriptionResult, setTranscriptionResult] = useState(null);
  const [error, setError] = useState('');
  const [options, setOptions] = useState({
    language: 'en',
    diarization: false,
    huggingfaceToken: '',
    temperature: 0.0
  });

  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      setError('');
      setTranscriptionResult(null);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      setSelectedFile(file);
      setError('');
      setTranscriptionResult(null);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const validateInputs = () => {
    if (!apiToken.trim()) {
      throw new Error('Please enter your Replicate API token');
    }
    if (!selectedFile) {
      throw new Error('Please select a media file');
    }
    if (options.diarization && !options.huggingfaceToken.trim()) {
      throw new Error('Hugging Face token is required when diarization is enabled');
    }
  };

  const startTranscription = async () => {
    try {
      validateInputs();
      
      setIsProcessing(true);
      setError('');
      setProgress(0);
      setTranscriptionResult(null);

      // Set API token
      replicateClient.setApiToken(apiToken);

      // Step 1: Convert audio
      setCurrentStep('Converting audio to WAV format...');
      const audioBlob = await audioConverter.convertToWav(selectedFile, (conversionProgress) => {
        setProgress(Math.round(conversionProgress * 0.3)); // 30% of total progress
      });

      // Step 2: Start transcription
      setCurrentStep('Starting transcription...');
      setProgress(30);
      
      const prediction = await replicateClient.startTranscription(audioBlob, options);
      
      // Step 3: Poll for completion
      setCurrentStep('Transcribing audio...');
      
      const result = await replicateClient.pollTranscription(
        prediction.id,
        (predictionUpdate) => {
          if (predictionUpdate.status === 'processing') {
            // Estimate progress based on time elapsed
            setProgress(30 + Math.min(65, Math.random() * 65)); // 30-95%
          }
        }
      );

      setProgress(100);
      setCurrentStep('Transcription completed!');
      setTranscriptionResult(result.output);
      
    } catch (err) {
      console.error('Transcription error:', err);
      setError(err.message);
    } finally {
      setIsProcessing(false);
      setCurrentStep('');
      setProgress(0);
    }
  };

  const handleDownloadAll = () => {
    if (transcriptionResult) {
      const baseFilename = selectedFile ? selectedFile.name.replace(/\.[^/.]+$/, '') : 'transcription';
      downloadAllFormats(transcriptionResult, baseFilename);
    }
  };

  const handleDownloadFormat = (format) => {
    if (!transcriptionResult) return;
    
    const baseFilename = selectedFile ? selectedFile.name.replace(/\.[^/.]+$/, '') : 'transcription';
    let content, filename, mimeType;
    
    switch (format) {
      case 'txt':
        content = generateTXT(transcriptionResult);
        filename = `${baseFilename}.txt`;
        mimeType = 'text/plain';
        break;
      case 'srt':
        content = generateSRT(transcriptionResult);
        filename = `${baseFilename}.srt`;
        mimeType = 'text/plain';
        break;
      case 'vtt':
        content = generateVTT(transcriptionResult);
        filename = `${baseFilename}.vtt`;
        mimeType = 'text/vtt';
        break;
      default:
        return;
    }
    
    downloadFile(content, filename, mimeType);
  };

  return (
    <div className="transcription-app">
      <div className="container">
        <h1>WhisperX Transcription Tool</h1>
        <p className="subtitle">Convert media files to text with precise timestamps</p>

        {/* API Token Input */}
        <div className="section">
          <label htmlFor="apiToken">Replicate API Token:</label>
          <input
            id="apiToken"
            type="password"
            value={apiToken}
            onChange={(e) => setApiToken(e.target.value)}
            placeholder="Enter your Replicate API token"
            disabled={isProcessing}
          />
          <small>Get your token from <a href="https://replicate.com/account/api-tokens" target="_blank" rel="noopener noreferrer">Replicate</a></small>
        </div>

        {/* File Upload */}
        <div className="section">
          <label>Media File:</label>
          <div 
            className={`file-drop-zone ${selectedFile ? 'has-file' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*,audio/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              disabled={isProcessing}
            />
            {selectedFile ? (
              <div className="file-info">
                <span className="file-name">{selectedFile.name}</span>
                <span className="file-size">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
              </div>
            ) : (
              <div className="file-placeholder">
                <span>Click to select or drag & drop a media file</span>
                <small>Supports video and audio formats</small>
              </div>
            )}
          </div>
        </div>

        {/* Options */}
        <div className="section">
          <h3>Transcription Options</h3>
          <div className="options-grid">
            <div className="option">
              <label htmlFor="language">Language:</label>
              <select
                id="language"
                value={options.language}
                onChange={(e) => setOptions({...options, language: e.target.value})}
                disabled={isProcessing}
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
                <option value="pt">Portuguese</option>
                <option value="ru">Russian</option>
                <option value="ja">Japanese</option>
                <option value="ko">Korean</option>
                <option value="zh">Chinese</option>
              </select>
            </div>
            
            <div className="option">
              <label>
                <input
                  type="checkbox"
                  checked={options.diarization}
                  onChange={(e) => setOptions({...options, diarization: e.target.checked})}
                  disabled={isProcessing}
                />
                Speaker Diarization
              </label>
            </div>
          </div>
          
          {options.diarization && (
            <div className="option">
              <label htmlFor="hfToken">Hugging Face Token:</label>
              <input
                id="hfToken"
                type="password"
                value={options.huggingfaceToken}
                onChange={(e) => setOptions({...options, huggingfaceToken: e.target.value})}
                placeholder="Required for speaker diarization"
                disabled={isProcessing}
              />
              <small>Get your token from <a href="https://huggingface.co/settings/tokens" target="_blank" rel="noopener noreferrer">Hugging Face</a></small>
            </div>
          )}
        </div>

        {/* Process Button */}
        <div className="section">
          <button 
            className="process-btn"
            onClick={startTranscription}
            disabled={isProcessing || !selectedFile || !apiToken.trim()}
          >
            {isProcessing ? 'Processing...' : 'Start Transcription'}
          </button>
        </div>

        {/* Progress */}
        {isProcessing && (
          <div className="section">
            <div className="progress-container">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <div className="progress-text">{currentStep} ({progress}%)</div>
            </div>
          </div>
        )}

        {/* Error */}
        {error && (
          <div className="section">
            <div className="error-message">
              <strong>Error:</strong> {error}
            </div>
          </div>
        )}

        {/* Results */}
        {transcriptionResult && (
          <div className="section">
            <h3>Transcription Complete!</h3>
            <div className="results-container">
              <div className="download-buttons">
                <button onClick={handleDownloadAll} className="download-btn primary">
                  Download All Formats
                </button>
                <button onClick={() => handleDownloadFormat('txt')} className="download-btn">
                  Download TXT
                </button>
                <button onClick={() => handleDownloadFormat('srt')} className="download-btn">
                  Download SRT
                </button>
                <button onClick={() => handleDownloadFormat('vtt')} className="download-btn">
                  Download VTT
                </button>
              </div>
              
              <div className="transcription-preview">
                <h4>Preview:</h4>
                <div className="preview-content">
                  {transcriptionResult.segments?.slice(0, 3).map((segment, index) => (
                    <div key={index} className="segment-preview">
                      <span className="timestamp">[{segment.start?.toFixed(2)}s - {segment.end?.toFixed(2)}s]</span>
                      <span className="text">{segment.text}</span>
                    </div>
                  ))}
                  {transcriptionResult.segments?.length > 3 && (
                    <div className="more-segments">
                      ... and {transcriptionResult.segments.length - 3} more segments
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranscriptionApp;
