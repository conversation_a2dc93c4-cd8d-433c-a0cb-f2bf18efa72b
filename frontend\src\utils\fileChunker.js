/**
 * Utility for handling large files by chunking them
 */

/**
 * Split a large file into smaller chunks for processing
 * @param {File} file - The file to split
 * @param {number} chunkSizeBytes - Size of each chunk in bytes (default: 90MB)
 * @returns {Promise<Array>} Array of file chunks
 */
export async function splitFileIntoChunks(file, chunkSizeBytes = 25 * 1024 * 1024) {
  const chunks = [];
  const totalChunks = Math.ceil(file.size / chunkSizeBytes);

  console.log(`📦 Splitting ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB) into ${totalChunks} chunks`);

  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSizeBytes;
    const end = Math.min(start + chunkSizeBytes, file.size);
    const chunk = file.slice(start, end);

    // Create a new File object for each chunk
    const chunkFile = new File(
      [chunk],
      `${file.name.split('.')[0]}_chunk_${i + 1}_of_${totalChunks}.${file.name.split('.').pop()}`,
      { type: file.type }
    );

    chunks.push({
      file: chunkFile,
      index: i,
      totalChunks: totalChunks,
      start: start,
      end: end,
      size: chunk.size,
      duration: null // Will be calculated if needed
    });

    console.log(`📄 Chunk ${i + 1}/${totalChunks}: ${(chunk.size / 1024 / 1024).toFixed(2)} MB`);
  }

  return chunks;
}

/**
 * Combine transcription results from multiple chunks
 * @param {Array} chunkResults - Array of transcription results from chunks
 * @returns {Object} Combined transcription result
 */
export function combineChunkResults(chunkResults) {
  if (!chunkResults || chunkResults.length === 0) {
    return { segments: [], word_segments: [] };
  }

  if (chunkResults.length === 1) {
    return chunkResults[0];
  }

  console.log(`🔗 Combining ${chunkResults.length} chunk results...`);

  const combinedResult = {
    segments: [],
    word_segments: []
  };

  let timeOffset = 0;

  chunkResults.forEach((result, index) => {
    if (!result || !result.segments) {
      console.warn(`⚠️ Chunk ${index + 1} has no segments, skipping`);
      return;
    }

    // Add segments with time offset
    result.segments.forEach(segment => {
      const adjustedSegment = {
        ...segment,
        start: segment.start + timeOffset,
        end: segment.end + timeOffset
      };

      // Adjust word-level timestamps if available
      if (segment.words) {
        adjustedSegment.words = segment.words.map(word => ({
          ...word,
          start: word.start + timeOffset,
          end: word.end + timeOffset
        }));
      }

      combinedResult.segments.push(adjustedSegment);
    });

    // Add word segments with time offset if available
    if (result.word_segments) {
      result.word_segments.forEach(wordSegment => {
        combinedResult.word_segments.push({
          ...wordSegment,
          start: wordSegment.start + timeOffset,
          end: wordSegment.end + timeOffset
        });
      });
    }

    // Update time offset for next chunk
    // Estimate chunk duration based on last segment end time
    if (result.segments.length > 0) {
      const lastSegment = result.segments[result.segments.length - 1];
      timeOffset = lastSegment.end;
    } else {
      // Fallback: estimate based on file size (rough approximation)
      timeOffset += 300; // 5 minutes per chunk as fallback
    }
  });

  console.log(`✅ Combined ${combinedResult.segments.length} segments from ${chunkResults.length} chunks`);

  return combinedResult;
}

/**
 * Extract audio from video using Web Audio API (limited functionality)
 * This is a fallback when FFmpeg.wasm is not available
 * @param {File} file - Video file
 * @returns {Promise<Blob>} Audio blob (if possible)
 */
export async function extractAudioWithWebAPI(file) {
  return new Promise((resolve, reject) => {
    try {
      // Create video element
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.onloadedmetadata = () => {
        // This is a simplified approach - in reality, extracting audio from video
        // in the browser without FFmpeg is very limited
        console.warn('⚠️ Web Audio API extraction is limited. Consider using smaller files or audio-only formats.');

        // For now, just return the original file
        // In a real implementation, you'd need a more sophisticated approach
        resolve(new Blob([file], { type: file.type }));
      };

      video.onerror = () => {
        reject(new Error('Failed to load video for audio extraction'));
      };

      video.src = URL.createObjectURL(file);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Check if file is likely to be accepted by Replicate
 * @param {File} file - File to check
 * @returns {Object} Validation result
 */
export function validateFileForReplicate(file) {
  const maxSize = 100 * 1024 * 1024; // 100MB
  const supportedTypes = [
    'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/flac', 'audio/aac',
    'video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm'
  ];

  const result = {
    isValid: true,
    warnings: [],
    errors: [],
    needsChunking: false,
    estimatedChunks: 0
  };

  // Check file size
  if (file.size > maxSize) {
    result.needsChunking = true;
    result.estimatedChunks = Math.ceil(file.size / (25 * 1024 * 1024)); // 25MB chunks
    result.warnings.push(`File is ${(file.size / 1024 / 1024).toFixed(2)} MB. Will be split into ${result.estimatedChunks} chunks.`);
  }

  // Check file type
  const isSupported = supportedTypes.some(type =>
    file.type.includes(type.split('/')[1]) ||
    file.name.toLowerCase().includes(type.split('/')[1])
  );

  if (!isSupported) {
    result.warnings.push(`File type "${file.type}" may not be supported. Supported: audio (MP3, WAV, FLAC, AAC) and video (MP4, AVI, MOV, MKV, WebM).`);
  }

  // Check for very large files that might cause issues
  if (file.size > 500 * 1024 * 1024) { // 500MB
    result.warnings.push('Very large file. Processing may take a long time and could fail.');
  }

  return result;
}

/**
 * Estimate processing time based on file size
 * @param {number} fileSizeBytes - File size in bytes
 * @returns {string} Estimated time string
 */
export function estimateProcessingTime(fileSizeBytes) {
  const sizeMB = fileSizeBytes / (1024 * 1024);

  // Rough estimates based on typical processing times
  if (sizeMB < 10) return '1-2 minutes';
  if (sizeMB < 50) return '2-5 minutes';
  if (sizeMB < 100) return '5-10 minutes';
  if (sizeMB < 200) return '10-15 minutes';
  if (sizeMB < 500) return '15-30 minutes';
  return '30+ minutes';
}

/**
 * Format file size for display
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size string
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
