{"name": "whisperx-proxy-server", "version": "1.0.0", "description": "Simple proxy server to handle CORS issues with Replicate API", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "axios": "^1.3.0"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["proxy", "replicate", "whisperx", "cors"], "author": "", "license": "MIT"}