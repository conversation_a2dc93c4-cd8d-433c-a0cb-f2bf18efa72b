/**
 * Utility functions for generating subtitle files from WhisperX output
 */

/**
 * Format time for SRT format (HH:MM:SS,mmm)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
function formatSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Format time for VTT format (HH:MM:SS.mmm)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
function formatVTTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Process WhisperX segments into subtitle entries
 * @param {Array} segments - WhisperX segments array
 * @returns {Array} - Processed subtitle entries
 */
function processSegments(segments) {
  if (!segments || !Array.isArray(segments)) {
    console.warn('Invalid segments data:', segments);
    return [];
  }

  return segments.map((segment, index) => ({
    index: index + 1,
    start: segment.start || 0,
    end: segment.end || segment.start || 0,
    text: segment.text ? segment.text.trim() : '',
    words: segment.words || []
  })).filter(entry => entry.text.length > 0);
}

/**
 * Generate TXT file content
 * @param {Object} transcriptionResult - WhisperX transcription result
 * @returns {string} - TXT file content
 */
export function generateTXT(transcriptionResult) {
  try {
    const segments = transcriptionResult.segments || [];
    const processedSegments = processSegments(segments);
    
    if (processedSegments.length === 0) {
      return 'No transcription content available.';
    }

    // Simple text format with timestamps
    const lines = processedSegments.map(segment => {
      const startTime = formatVTTTime(segment.start);
      const endTime = formatVTTTime(segment.end);
      return `[${startTime} --> ${endTime}] ${segment.text}`;
    });

    return lines.join('\n\n');
  } catch (error) {
    console.error('Error generating TXT:', error);
    return 'Error generating transcription text.';
  }
}

/**
 * Generate SRT file content
 * @param {Object} transcriptionResult - WhisperX transcription result
 * @returns {string} - SRT file content
 */
export function generateSRT(transcriptionResult) {
  try {
    const segments = transcriptionResult.segments || [];
    const processedSegments = processSegments(segments);
    
    if (processedSegments.length === 0) {
      return '1\n00:00:00,000 --> 00:00:01,000\nNo transcription content available.';
    }

    const srtEntries = processedSegments.map(segment => {
      const startTime = formatSRTTime(segment.start);
      const endTime = formatSRTTime(segment.end);
      
      return `${segment.index}\n${startTime} --> ${endTime}\n${segment.text}`;
    });

    return srtEntries.join('\n\n');
  } catch (error) {
    console.error('Error generating SRT:', error);
    return '1\n00:00:00,000 --> 00:00:01,000\nError generating subtitles.';
  }
}

/**
 * Generate VTT file content
 * @param {Object} transcriptionResult - WhisperX transcription result
 * @returns {string} - VTT file content
 */
export function generateVTT(transcriptionResult) {
  try {
    const segments = transcriptionResult.segments || [];
    const processedSegments = processSegments(segments);
    
    let vttContent = 'WEBVTT\n\n';
    
    if (processedSegments.length === 0) {
      vttContent += '00:00:00.000 --> 00:00:01.000\nNo transcription content available.';
      return vttContent;
    }

    const vttEntries = processedSegments.map(segment => {
      const startTime = formatVTTTime(segment.start);
      const endTime = formatVTTTime(segment.end);
      
      return `${startTime} --> ${endTime}\n${segment.text}`;
    });

    vttContent += vttEntries.join('\n\n');
    return vttContent;
  } catch (error) {
    console.error('Error generating VTT:', error);
    return 'WEBVTT\n\n00:00:00.000 --> 00:00:01.000\nError generating subtitles.';
  }
}

/**
 * Create and download a file
 * @param {string} content - File content
 * @param {string} filename - File name
 * @param {string} mimeType - MIME type
 */
export function downloadFile(content, filename, mimeType = 'text/plain') {
  try {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    setTimeout(() => URL.revokeObjectURL(url), 100);
    
    console.log(`Downloaded ${filename}`);
  } catch (error) {
    console.error(`Error downloading ${filename}:`, error);
    alert(`Failed to download ${filename}`);
  }
}

/**
 * Generate and download all subtitle formats
 * @param {Object} transcriptionResult - WhisperX transcription result
 * @param {string} baseFilename - Base filename (without extension)
 */
export function downloadAllFormats(transcriptionResult, baseFilename = 'transcription') {
  try {
    // Generate content for all formats
    const txtContent = generateTXT(transcriptionResult);
    const srtContent = generateSRT(transcriptionResult);
    const vttContent = generateVTT(transcriptionResult);
    
    // Download all files
    downloadFile(txtContent, `${baseFilename}.txt`, 'text/plain');
    downloadFile(srtContent, `${baseFilename}.srt`, 'text/plain');
    downloadFile(vttContent, `${baseFilename}.vtt`, 'text/vtt');
    
    console.log('All subtitle formats downloaded successfully');
  } catch (error) {
    console.error('Error downloading subtitle formats:', error);
    alert('Failed to download subtitle files');
  }
}
