/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* App container */
.App {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Transcription App */
.transcription-app {
  width: 100%;
  max-width: 800px;
}

.container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.transcription-app h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.subtitle {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Section styles */
.section {
  margin-bottom: 2rem;
}

.section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.section input[type="text"],
.section input[type="password"],
.section select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e6ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.section input:focus,
.section select:focus {
  outline: none;
  border-color: #667eea;
}

.section small {
  display: block;
  margin-top: 0.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.section small a {
  color: #667eea;
  text-decoration: none;
}

.section small a:hover {
  text-decoration: underline;
}

/* File drop zone */
.file-drop-zone {
  border: 2px dashed #bdc3c7;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-zone:hover {
  border-color: #667eea;
  background: #f0f3ff;
}

.file-drop-zone.has-file {
  border-color: #27ae60;
  background: #f0fff4;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-placeholder span {
  font-size: 1.1rem;
  color: #2c3e50;
  font-weight: 500;
}

.file-placeholder small {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.file-size {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.file-details {
  margin-top: 0.5rem;
}

.file-details small {
  color: #7f8c8d;
  font-size: 0.8rem;
}

.file-validation {
  margin-top: 0.5rem;
}

.file-warning {
  color: #e67e22;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

.file-error {
  color: #e74c3c;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Options */
.section h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.option {
  display: flex;
  flex-direction: column;
}

.option label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0;
}

.option input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Process button */
.process-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.process-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Progress */
.progress-container {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #ecf0f1;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progress-text {
  color: #2c3e50;
  font-weight: 500;
  font-size: 1rem;
}

.progress-details {
  margin-top: 0.5rem;
}

.progress-details small {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Error message */
.error-message {
  background: #fdeaea;
  border: 1px solid #e74c3c;
  color: #c0392b;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 500;
}

/* Results */
.results-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.download-buttons {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.download-btn {
  background: #34495e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.download-btn:hover {
  background: #2c3e50;
  transform: translateY(-1px);
}

.download-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.download-btn.primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Transcription preview */
.transcription-preview {
  margin-top: 1rem;
}

.transcription-preview h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.preview-content {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.segment-preview {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ecf0f1;
}

.segment-preview:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.timestamp {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: 500;
}

.text {
  color: #2c3e50;
  line-height: 1.5;
}

.more-segments {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  margin-top: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .App {
    padding: 1rem;
  }

  .container {
    padding: 1.5rem;
  }

  .transcription-app h1 {
    font-size: 2rem;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .download-buttons {
    flex-direction: column;
  }

  .file-drop-zone {
    padding: 2rem 1rem;
  }
}
