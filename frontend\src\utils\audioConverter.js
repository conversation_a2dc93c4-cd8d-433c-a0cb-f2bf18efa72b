import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';

class AudioConverter {
  constructor() {
    this.ffmpeg = new FFmpeg();
    this.loaded = false;
  }

  async load() {
    if (this.loaded) return;

    try {
      // Check if we're in a secure context (required for SharedArrayBuffer)
      if (!window.isSecureContext) {
        throw new Error('FFmpeg.wasm requires a secure context (HTTPS)');
      }

      // Check if SharedArrayBuffer is available
      if (typeof SharedArrayBuffer === 'undefined') {
        throw new Error('FFmpeg.wasm requires SharedArrayBuffer support');
      }

      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      this.loaded = true;
      console.log('FFmpeg loaded successfully');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      this.loaded = false;
      throw new Error(`Failed to initialize audio converter: ${error.message}`);
    }
  }

  /**
   * Convert media file to WAV format optimized for WhisperX
   * @param {File} file - Input media file
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Blob>} - Converted WAV file as blob or original file
   */
  async convertToWav(file, onProgress = () => {}) {
    try {
      // Try to load FFmpeg first
      if (!this.loaded) {
        await this.load();
      }

      const inputFileName = `input.${file.name.split('.').pop()}`;
      const outputFileName = 'output.wav';

      // Write input file to FFmpeg filesystem
      await this.ffmpeg.writeFile(inputFileName, await fetchFile(file));

      // Set up progress monitoring
      this.ffmpeg.on('progress', ({ progress }) => {
        onProgress(Math.round(progress * 100));
      });

      // Convert to WAV with optimal settings for WhisperX:
      // - 16kHz sample rate
      // - Mono channel
      // - 16-bit PCM
      // - Volume normalization
      await this.ffmpeg.exec([
        '-i', inputFileName,
        '-vn',                    // No video
        '-acodec', 'pcm_s16le',   // 16-bit PCM
        '-ar', '16000',           // 16kHz sample rate
        '-ac', '1',               // Mono channel
        '-af', 'loudnorm',        // Volume normalization
        outputFileName
      ]);

      // Read the output file
      const data = await this.ffmpeg.readFile(outputFileName);

      // Clean up
      await this.ffmpeg.deleteFile(inputFileName);
      await this.ffmpeg.deleteFile(outputFileName);

      // Convert to blob
      const blob = new Blob([data.buffer], { type: 'audio/wav' });

      console.log(`Converted ${file.name} to WAV format (${(blob.size / 1024 / 1024).toFixed(2)} MB)`);

      return blob;
    } catch (error) {
      console.error('Audio conversion failed, using original file:', error);

      // Fallback: return original file as blob
      console.warn('⚠️ Using original file without conversion. WhisperX will handle the audio format.');
      onProgress(100); // Mark as complete

      return new Blob([file], { type: file.type });
    }
  }

  /**
   * Check if FFmpeg.wasm can be loaded in this environment
   * @returns {Promise<boolean>} - Whether FFmpeg can be used
   */
  async canUseFFmpeg() {
    try {
      if (!window.isSecureContext) return false;
      if (typeof SharedArrayBuffer === 'undefined') return false;
      await this.load();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get audio duration from file
   * @param {File} file - Input media file
   * @returns {Promise<number>} - Duration in seconds
   */
  async getDuration(file) {
    if (!this.loaded) {
      await this.load();
    }

    try {
      const inputFileName = `input.${file.name.split('.').pop()}`;

      await this.ffmpeg.writeFile(inputFileName, await fetchFile(file));

      // Get duration using ffprobe-like functionality
      await this.ffmpeg.exec([
        '-i', inputFileName,
        '-f', 'null',
        '-'
      ]);

      await this.ffmpeg.deleteFile(inputFileName);

      // Note: In a real implementation, you'd parse the FFmpeg output
      // For now, we'll return a placeholder
      return 0;
    } catch (error) {
      console.error('Failed to get duration:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const audioConverter = new AudioConverter();
