import axios from 'axios';

class ReplicateClient {
  constructor() {
    this.apiToken = null;
    this.baseURL = 'https://api.replicate.com/v1';
    this.modelVersion = 'victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb';
  }

  setApiToken(token) {
    this.apiToken = token;
  }

  /**
   * Convert blob to base64 data URL
   * @param {Blob} blob - Audio blob
   * @returns {Promise<string>} - Base64 data URL
   */
  async blobToDataURL(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Start transcription job
   * @param {Blob} audioBlob - WAV audio blob
   * @param {Object} options - Transcription options
   * @returns {Promise<Object>} - Prediction object with job ID
   */
  async startTranscription(audioBlob, options = {}) {
    if (!this.apiToken) {
      throw new Error('Replicate API token not set');
    }

    try {
      // Convert blob to data URL
      const audioDataURL = await this.blobToDataURL(audioBlob);

      const payload = {
        version: this.modelVersion,
        input: {
          audio_file: audioDataURL,
          language: options.language || 'en',
          temperature: options.temperature || 0.0,
          align_output: true,
          diarization: options.diarization || false,
          huggingface_access_token: options.huggingfaceToken || '',
          min_speakers: options.minSpeakers || null,
          max_speakers: options.maxSpeakers || null,
          language_detection_min_prob: options.languageDetectionMinProb || 0,
          language_detection_max_tries: options.languageDetectionMaxTries || 5,
          batch_size: options.batchSize || 64,
          vad_onset: options.vadOnset || 0.500,
          vad_offset: options.vadOffset || 0.363,
          debug: options.debug || false
        }
      };

      console.log('Starting transcription with payload:', {
        ...payload,
        input: {
          ...payload.input,
          audio_file: '[AUDIO_DATA]' // Don't log the actual audio data
        }
      });

      const response = await axios.post(
        `${this.baseURL}/predictions`,
        payload,
        {
          headers: {
            'Authorization': `Token ${this.apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Transcription job started:', response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to start transcription:', error);
      if (error.response) {
        throw new Error(`Replicate API error: ${error.response.status} - ${error.response.data?.detail || error.response.statusText}`);
      }
      throw new Error(`Network error: ${error.message}`);
    }
  }

  /**
   * Get transcription status
   * @param {string} predictionId - Prediction ID from startTranscription
   * @returns {Promise<Object>} - Prediction object with status and output
   */
  async getTranscriptionStatus(predictionId) {
    if (!this.apiToken) {
      throw new Error('Replicate API token not set');
    }

    try {
      const response = await axios.get(
        `${this.baseURL}/predictions/${predictionId}`,
        {
          headers: {
            'Authorization': `Token ${this.apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get transcription status:', error);
      if (error.response) {
        throw new Error(`Replicate API error: ${error.response.status} - ${error.response.data?.detail || error.response.statusText}`);
      }
      throw new Error(`Network error: ${error.message}`);
    }
  }

  /**
   * Poll for transcription completion
   * @param {string} predictionId - Prediction ID
   * @param {Function} onProgress - Progress callback
   * @param {number} maxWaitTime - Maximum wait time in milliseconds
   * @returns {Promise<Object>} - Final prediction result
   */
  async pollTranscription(predictionId, onProgress = () => {}, maxWaitTime = 600000) {
    const startTime = Date.now();
    const pollInterval = 2000; // 2 seconds

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const prediction = await this.getTranscriptionStatus(predictionId);
        
        onProgress(prediction);

        if (prediction.status === 'succeeded') {
          console.log('Transcription completed successfully');
          return prediction;
        }

        if (prediction.status === 'failed') {
          throw new Error(`Transcription failed: ${prediction.error || 'Unknown error'}`);
        }

        if (prediction.status === 'canceled') {
          throw new Error('Transcription was canceled');
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.error('Error polling transcription:', error);
        throw error;
      }
    }

    throw new Error('Transcription timed out');
  }
}

// Export singleton instance
export const replicateClient = new ReplicateClient();
