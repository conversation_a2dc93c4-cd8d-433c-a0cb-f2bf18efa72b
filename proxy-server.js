// Simple proxy server to handle CORS issues with Replicate API
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 5001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json({ limit: '500mb' }));
app.use(express.urlencoded({ extended: true, limit: '500mb' }));

// Configure multer for file uploads
const upload = multer({
  dest: 'temp-uploads/',
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

// Ensure temp directory exists
if (!fs.existsSync('temp-uploads')) {
  fs.mkdirSync('temp-uploads');
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Proxy server is running' });
});

// Proxy endpoint for Replicate API
app.post('/api/replicate/predictions', upload.single('audio_file'), async (req, res) => {
  try {
    console.log('📥 Received transcription request');

    const { replicate_token, ...otherParams } = req.body;

    if (!replicate_token) {
      return res.status(400).json({ error: 'Replicate token is required' });
    }

    let audioData;

    if (req.file) {
      // File was uploaded
      console.log(`📁 Processing uploaded file: ${req.file.originalname} (${(req.file.size / 1024 / 1024).toFixed(2)} MB)`);
      audioData = fs.readFileSync(req.file.path);

      // Clean up temp file
      fs.unlinkSync(req.file.path);
    } else if (req.body.audio_data) {
      // Base64 data was sent
      console.log('📄 Processing base64 audio data');
      audioData = Buffer.from(req.body.audio_data, 'base64');
    } else {
      return res.status(400).json({ error: 'No audio file or data provided' });
    }

    // Convert to base64 for Replicate API
    const base64Audio = `data:audio/wav;base64,${audioData.toString('base64')}`;

    console.log(`📤 Sending to Replicate API (${(audioData.length / 1024 / 1024).toFixed(2)} MB)`);

    // Prepare payload for Replicate
    const payload = {
      version: 'victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb',
      input: {
        audio_file: base64Audio,
        language: otherParams.language || 'en',
        temperature: parseFloat(otherParams.temperature) || 0.0,
        align_output: otherParams.align_output === 'true' || true,
        diarization: otherParams.diarization === 'true' || false,
        huggingface_access_token: otherParams.huggingface_access_token || '',
        min_speakers: otherParams.min_speakers && otherParams.min_speakers !== '' ? parseInt(otherParams.min_speakers) : undefined,
        max_speakers: otherParams.max_speakers && otherParams.max_speakers !== '' ? parseInt(otherParams.max_speakers) : undefined,
        language_detection_min_prob: parseFloat(otherParams.language_detection_min_prob) || 0,
        language_detection_max_tries: parseInt(otherParams.language_detection_max_tries) || 5,
        batch_size: parseInt(otherParams.batch_size) || 64,
        vad_onset: parseFloat(otherParams.vad_onset) || 0.500,
        vad_offset: parseFloat(otherParams.vad_offset) || 0.363,
        debug: otherParams.debug === 'true' || false
      }
    };

    // Make request to Replicate API
    const response = await axios.post('https://api.replicate.com/v1/predictions', payload, {
      headers: {
        'Authorization': `Token ${replicate_token}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    console.log(`✅ Replicate API response: ${response.data.id}`);
    res.json(response.data);

  } catch (error) {
    console.error('❌ Proxy error:', error.message);

    if (error.response) {
      // Replicate API error
      console.error('Replicate API error:', error.response.status, error.response.data);
      res.status(error.response.status).json({
        error: 'Replicate API error',
        details: error.response.data,
        status: error.response.status
      });
    } else if (error.code === 'ECONNABORTED') {
      // Timeout error
      res.status(408).json({
        error: 'Request timeout',
        message: 'The request to Replicate API timed out'
      });
    } else {
      // Other errors
      res.status(500).json({
        error: 'Proxy server error',
        message: error.message
      });
    }
  }
});

// Proxy endpoint for checking prediction status
app.get('/api/replicate/predictions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { replicate_token } = req.query;

    if (!replicate_token) {
      return res.status(400).json({ error: 'Replicate token is required' });
    }

    const response = await axios.get(`https://api.replicate.com/v1/predictions/${id}`, {
      headers: {
        'Authorization': `Token ${replicate_token}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);

  } catch (error) {
    console.error('❌ Status check error:', error.message);

    if (error.response) {
      res.status(error.response.status).json({
        error: 'Replicate API error',
        details: error.response.data
      });
    } else {
      res.status(500).json({
        error: 'Proxy server error',
        message: error.message
      });
    }
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
  console.log(`📡 Ready to proxy requests to Replicate API`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down proxy server...');

  // Clean up temp directory
  if (fs.existsSync('temp-uploads')) {
    const files = fs.readdirSync('temp-uploads');
    files.forEach(file => {
      fs.unlinkSync(path.join('temp-uploads', file));
    });
    fs.rmdirSync('temp-uploads');
  }

  process.exit(0);
});
