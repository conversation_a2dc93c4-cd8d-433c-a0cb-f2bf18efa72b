// API Configuration
// Replace the token below with your actual Replicate API token
// Get your token from: https://replicate.com/account/api-tokens

export const REPLICATE_API_TOKEN = 'YOUR_REPLICATE_API_TOKEN_HERE';

// WhisperX model configuration
export const WHISPERX_MODEL = 'victor-upmeet/whisperx:84d2ad2d6194fe98a17d2b60bef1c7f910c46b2f6fd38996ca457afd9c8abfcb';

// Default transcription options
export const DEFAULT_OPTIONS = {
  language: 'en',
  temperature: 0.0,
  align_output: true,
  diarization: false,
  language_detection_min_prob: 0,
  language_detection_max_tries: 5,
  batch_size: 64,
  vad_onset: 0.500,
  vad_offset: 0.363,
  debug: false
};
