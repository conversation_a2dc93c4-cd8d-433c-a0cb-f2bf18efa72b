{"name": "whisperx-frontend", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "axios": "^1.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "replicate": "^0.20.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^6.3.5"}}